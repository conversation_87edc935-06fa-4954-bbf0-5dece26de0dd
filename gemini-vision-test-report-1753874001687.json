{"totalTests": 8, "passedTests": 2, "failedTests": 6, "errors": [{"test": "API Key Rotation Manager", "error": "No API keys available for testing", "timestamp": "2025-07-30T11:13:09.738Z"}, {"test": "Token Optimization Engine", "error": "Compact prompt missing essential elements", "timestamp": "2025-07-30T11:13:10.213Z"}, {"test": "Gemini Vision Analysis Service", "error": "No API keys available for testing", "timestamp": "2025-07-30T11:13:10.253Z"}, {"test": "Enhanced Chart Analysis Engine", "error": "No API keys available for testing", "timestamp": "2025-07-30T11:13:10.411Z"}, {"test": "Multi-API Key Workflow", "error": "No successful analyses in multi-key workflow", "timestamp": "2025-07-30T11:13:21.324Z"}, {"test": "Real Screenshot Analysis", "error": "No API keys available for real screenshot testing", "timestamp": "2025-07-30T11:13:21.508Z"}], "performance": {}, "apiKeyTests": {}, "tokenOptimization": {"imageCompression": {"originalSize": 0, "optimizedSize": 0, "compressionRatio": "NaN%"}, "promptCompression": {"originalLength": 192, "optimizedLength": 139, "compressionRatio": "27.60%"}, "efficiency": {"totalTokensUsed": 21251, "averageTokensPerRequest": 1416.7333333333333, "dailyLimitUsage": "42.50%", "estimatedDailyCapacity": 35, "cacheHitRate": "26.67%"}}, "analysisResults": []}